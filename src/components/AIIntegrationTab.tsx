// AI集成标签页组件

import React, { useState, useCallback, useEffect } from 'react'
import {
  Bo<PERSON>,
  Plus,
  Settings,
  Eye,
  EyeOff,
  Trash2,
  Edit,
  CheckCircle,
  AlertCircle,
  Loader2,
  Globe,
  Key,
  Zap,
  RefreshCw,
  Download,
  Upload,
  MessageCircle
} from 'lucide-react'
import { But<PERSON> } from './ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card'
import { Alert, AlertDescription } from './ui/alert'
import { Input } from './ui/input'
import { Label } from './ui/label'
import { Switch } from './ui/switch'
import { Badge } from './ui/badge'
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from './ui/dialog'
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from './ui/select'

// 导入AI集成服务
import { aiIntegrationService } from '../services/aiIntegrationService'
import { AIProviderConfig, AIProviderInfo, AIConnectionResult } from '../types/ai'
import ModelSelector from './ModelSelector'

interface AIIntegrationTabProps {}

const AIIntegrationTab: React.FC<AIIntegrationTabProps> = () => {
  // 状态管理
  const [providers, setProviders] = useState<AIProviderConfig[]>([])
  const [supportedProviders, setSupportedProviders] = useState<AIProviderInfo[]>([])
  const [connectionResults, setConnectionResults] = useState<Record<string, AIConnectionResult>>({})
  const [providerModels, setProviderModels] = useState<Record<string, any[]>>({})
  const [selectedModels, setSelectedModels] = useState<Record<string, string>>({}) // providerId -> modelId
  const [loadingModels, setLoadingModels] = useState<Record<string, boolean>>({})
  const [isLoading, setIsLoading] = useState(true)
  const [showAddDialog, setShowAddDialog] = useState(false)
  const [editingProvider, setEditingProvider] = useState<AIProviderConfig | null>(null)
  const [showApiKey, setShowApiKey] = useState<Record<string, boolean>>({})
  const [testingProvider, setTestingProvider] = useState<string | null>(null)
  const [testingAll, setTestingAll] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // 模型测试相关状态
  const [testingModel, setTestingModel] = useState<string | null>(null)
  const [modelTestResults, setModelTestResults] = useState<Record<string, {
    success: boolean
    response?: string
    error?: string
    modelName?: string
    timestamp: Date
  }>>({})
  
  // 新提供商表单状态
  const [newProvider, setNewProvider] = useState<{
    name: string
    type: string
    baseUrl: string
    apiKey: string
    headers?: Record<string, string>
    timeout?: number
    enabled: boolean
  }>({
    name: '',
    type: 'openai',
    baseUrl: '',
    apiKey: '',
    enabled: true
  })

  // 初始化数据
  useEffect(() => {
    loadData()
    loadSelectedModels()
  }, [])

  // 加载已选择的模型
  const loadSelectedModels = useCallback(async () => {
    try {
      const savedModels = await aiIntegrationService.getSelectedModels()
      const modelMap: Record<string, string> = {}
      
      Object.entries(savedModels).forEach(([providerId, modelInfo]) => {
        modelMap[providerId] = modelInfo.modelId
      })
      
      setSelectedModels(modelMap)
      console.log('已加载保存的模型选择:', modelMap)
    } catch (error) {
      console.error('加载模型选择失败:', error)
    }
  }, [])

  // 加载数据
  const loadData = useCallback(async () => {
    try {
      setIsLoading(true)
      setError(null)
      
      // 获取支持的提供商列表
      const supportedList = aiIntegrationService.getSupportedProviders()
      setSupportedProviders(supportedList)
      
      // 获取已配置的提供商
      const configuredList = await aiIntegrationService.getConfiguredProviders()
      setProviders(configuredList)
      
      console.log('AI集成数据加载完成:', {
        supported: supportedList.length,
        configured: configuredList.length
      })
    } catch (error) {
      console.error('加载AI集成数据失败:', error)
      setError('加载数据失败，请刷新页面重试')
    } finally {
      setIsLoading(false)
    }
  }, [])

  // 添加新提供商
  const handleAddProvider = useCallback(async () => {
    if (!newProvider.name.trim() || !newProvider.baseUrl.trim()) {
      setError('请填写提供商名称和API地址')
      return
    }
    
    try {
      setError(null)
      await aiIntegrationService.configureProvider({
        name: newProvider.name,
        type: newProvider.type as any,
        baseUrl: newProvider.baseUrl,
        apiKey: newProvider.apiKey,
        headers: newProvider.headers,
        timeout: newProvider.timeout,
        enabled: newProvider.enabled
      })
      
      // 重新加载数据
      await loadData()
      
      // 自动测试新添加的提供商连接
      const providers = await aiIntegrationService.getConfiguredProviders()
      const newProviderConfig = providers.find(p => p.name === newProvider.name)
      if (newProviderConfig) {
        console.log('自动测试新提供商连接:', newProviderConfig.name)
        await handleTestProvider(newProviderConfig.id)
      }
      
      // 重置表单
      setShowAddDialog(false)
      setNewProvider({
        name: '',
        type: 'openai',
        baseUrl: '',
        apiKey: '',
        enabled: true
      })
      
      console.log('AI提供商添加成功:', newProvider.name)
    } catch (error) {
      console.error('添加AI提供商失败:', error)
      setError((error as Error).message)
    }
  }, [newProvider, loadData])

  // 删除提供商
  const handleDeleteProvider = useCallback(async (id: string) => {
    try {
      setError(null)
      await aiIntegrationService.removeProvider(id)
      await loadData()
      console.log('AI提供商删除成功:', id)
    } catch (error) {
      console.error('删除AI提供商失败:', error)
      setError((error as Error).message)
    }
  }, [loadData])

  // 切换提供商启用状态
  const handleToggleProvider = useCallback(async (id: string) => {
    try {
      setError(null)
      const provider = providers.find(p => p.id === id)
      if (!provider) return
      
      await aiIntegrationService.updateProvider(id, {
        enabled: !provider.enabled
      })
      await loadData()
      console.log('AI提供商状态切换成功:', id)
    } catch (error) {
      console.error('切换AI提供商状态失败:', error)
      setError((error as Error).message)
    }
  }, [providers, loadData])

  // 测试提供商连接
  const handleTestProvider = useCallback(async (id: string) => {
    try {
      setTestingProvider(id)
      setError(null)
      
      const result = await aiIntegrationService.testConnection(id)
      setConnectionResults(prev => ({ ...prev, [id]: result }))
      
      // 如果连接成功，自动获取模型列表
      if (result.success) {
        await loadProviderModels(id)
      }
      
      console.log('AI提供商连接测试完成:', id, result)
    } catch (error) {
      console.error('测试AI提供商连接失败:', error)
      setError((error as Error).message)
    } finally {
      setTestingProvider(null)
    }
  }, [])

  // 测试模型对话功能
  const handleTestModel = useCallback(async (providerId: string) => {
    try {
      setTestingModel(providerId)
      setError(null)

      const provider = providers.find(p => p.id === providerId)
      if (!provider) {
        throw new Error('提供商不存在')
      }

      // 获取当前可用的模型列表
      const availableModels = providerModels[providerId] || []
      if (availableModels.length === 0) {
        throw new Error('没有可用的模型，请先加载模型列表')
      }

      // 优先使用已选择的模型，如果不可用则自动选择一个可用模型
      let modelId = selectedModels[providerId]
      let selectedModel = availableModels.find(m => m.id === modelId)

      if (!selectedModel) {
        // 如果保存的模型不可用，自动选择第一个推荐模型或第一个可用模型
        selectedModel = availableModels.find(m => m.isRecommended) || availableModels[0]
        modelId = selectedModel.id

        console.log(`保存的模型不可用，自动选择模型: ${selectedModel.displayName} (${modelId})`)

        // 更新选择的模型
        await aiIntegrationService.saveSelectedModel(providerId, modelId)
        setSelectedModels(prev => ({ ...prev, [providerId]: modelId }))
      }

      console.log('开始测试模型对话:', {
        providerId,
        modelId,
        modelName: selectedModel.displayName
      })

      // 发送测试消息到background script
      const response = await chrome.runtime.sendMessage({
        type: 'AI_GENERATE_TEXT',
        data: {
          prompt: '你好，请简单回复一下确认你能正常工作。',
          generationType: 'test',
          context: {
            providerId,
            modelId
          },
          maxLength: 100
        }
      })

      if (response?.success) {
        setModelTestResults(prev => ({
          ...prev,
          [providerId]: {
            success: true,
            response: response.content,
            modelName: selectedModel.displayName,
            timestamp: new Date()
          }
        }))
        console.log('模型测试成功:', response.content)
      } else {
        throw new Error(response?.error || '模型测试失败')
      }
    } catch (error) {
      console.error('模型测试失败:', error)
      setModelTestResults(prev => ({
        ...prev,
        [providerId]: {
          success: false,
          error: (error as Error).message,
          timestamp: new Date()
        }
      }))
      setError((error as Error).message)
    } finally {
      setTestingModel(null)
    }
  }, [providers, selectedModels, providerModels])

  // 测试所有提供商连接
  const handleTestAllProviders = useCallback(async () => {
    try {
      setTestingAll(true)
      setError(null)
      
      const results = await aiIntegrationService.testAllConnections()
      const resultsMap = results.reduce((acc, result) => {
        acc[result.providerId] = result
        return acc
      }, {} as Record<string, AIConnectionResult>)
      
      setConnectionResults(resultsMap)
      console.log('所有AI提供商连接测试完成:', results)
    } catch (error) {
      console.error('测试所有AI提供商连接失败:', error)
      setError((error as Error).message)
    } finally {
      setTestingAll(false)
    }
  }, [])

  // 切换API密钥显示
  const toggleApiKeyVisibility = useCallback((id: string) => {
    setShowApiKey(prev => ({ ...prev, [id]: !prev[id] }))
  }, [])

  // 获取提供商信息
  const getProviderInfo = useCallback((type: string) => {
    return supportedProviders.find(p => p.type === type) || supportedProviders[0]
  }, [supportedProviders])

  // 获取连接状态
  const getConnectionStatus = useCallback((providerId: string) => {
    const result = connectionResults[providerId]
    if (testingProvider === providerId) return 'testing'
    if (!result) return 'unknown'
    return result.success ? 'connected' : 'error'
  }, [connectionResults, testingProvider])

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected': return 'bg-green-500'
      case 'error': return 'bg-red-500'
      case 'testing': return 'bg-yellow-500'
      default: return 'bg-gray-500'
    }
  }

  // 获取状态文本
  const getStatusText = (status: string) => {
    switch (status) {
      case 'connected': return '已连接'
      case 'error': return '连接失败'
      case 'testing': return '测试中'
      default: return '未测试'
    }
  }

  // 处理提供商类型变化
  const handleProviderTypeChange = useCallback((type: string) => {
    const providerInfo = supportedProviders.find(p => p.type === type)
    setNewProvider(prev => ({
      ...prev,
      type,
      baseUrl: providerInfo?.defaultBaseUrl || ''
    }))
  }, [supportedProviders])

  // 加载提供商的模型列表
  const loadProviderModels = useCallback(async (providerId: string) => {
    try {
      setLoadingModels(prev => ({ ...prev, [providerId]: true }))
      const models = await aiIntegrationService.getAvailableModels(providerId)
      setProviderModels(prev => ({ ...prev, [providerId]: models }))
      console.log(`加载模型列表成功: ${providerId} - ${models.length} 个模型`)
    } catch (error) {
      console.error('加载模型列表失败:', error)
      setError(`加载模型列表失败: ${(error as Error).message}`)
    } finally {
      setLoadingModels(prev => ({ ...prev, [providerId]: false }))
    }
  }, [])

  // 选择模型
  const handleSelectModel = useCallback(async (providerId: string, modelId: string) => {
    try {
      if (modelId) {
        // 保存模型选择
        await aiIntegrationService.saveSelectedModel(providerId, modelId)
        setSelectedModels(prev => ({ ...prev, [providerId]: modelId }))
        console.log('模型选择已保存:', { providerId, modelId })
      } else {
        // 移除模型选择
        await aiIntegrationService.removeSelectedModel(providerId)
        setSelectedModels(prev => {
          const newSelected = { ...prev }
          delete newSelected[providerId]
          return newSelected
        })
        console.log('模型选择已移除:', providerId)
      }
    } catch (error) {
      console.error('保存模型选择失败:', error)
      setError(`保存模型选择失败: ${(error as Error).message}`)
    }
  }, [])

  // 自动加载已连接提供商的模型
  const autoLoadModelsForConnectedProviders = useCallback(async () => {
    for (const provider of providers) {
      const connectionResult = connectionResults[provider.id]
      if (connectionResult?.success && !providerModels[provider.id] && !loadingModels[provider.id]) {
        await loadProviderModels(provider.id)
      }
    }
  }, [providers, connectionResults, providerModels, loadingModels, loadProviderModels])

  // 当连接结果更新时，自动加载模型
  useEffect(() => {
    autoLoadModelsForConnectedProviders()
  }, [autoLoadModelsForConnectedProviders])

  // 如果正在加载，显示加载状态
  if (isLoading) {
    return (
      <div className="p-6 space-y-8 bg-background text-foreground">
        <Card>
          <CardContent className="p-8 text-center">
            <Loader2 className="w-8 h-8 mx-auto mb-4 animate-spin text-primary" />
            <h3 className="text-lg font-semibold mb-2">加载中...</h3>
            <p className="text-muted-foreground">正在加载AI集成配置</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-8 bg-background text-foreground">
      {/* 页面标题 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Bot className="w-6 h-6 mr-3 text-primary" />
            AI集成管理
          </CardTitle>
          <CardDescription>
            管理和配置各种AI提供商，支持OpenAI、Claude、DeepSeek、智谱AI、通义千问等多种服务
          </CardDescription>
        </CardHeader>
      </Card>

      {/* 错误提示 */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* 已选模型摘要 */}
      {Object.keys(selectedModels).length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-base">
              <CheckCircle className="w-5 h-5 mr-2 text-green-500" />
              已选择的模型
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-2">
              {Object.entries(selectedModels).map(([providerId, modelId]) => {
                const provider = providers.find(p => p.id === providerId)
                const providerInfo = getProviderInfo(provider?.type || '')
                return (
                  <div key={providerId} className="flex items-center justify-between p-2 bg-muted/30 rounded">
                    <div className="flex items-center space-x-3">
                      <span className="text-lg">{providerInfo?.icon || '🤖'}</span>
                      <div>
                        <p className="font-medium text-sm">{provider?.name}</p>
                        <p className="font-mono text-xs text-muted-foreground">{modelId}</p>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        setSelectedModels(prev => {
                          const newSelected = { ...prev }
                          delete newSelected[providerId]
                          return newSelected
                        })
                      }}
                    >
                      <Trash2 className="w-3 h-3" />
                    </Button>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 操作栏 */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-lg font-semibold">AI提供商配置</h2>
          <p className="text-sm text-muted-foreground">
            共 {providers.length} 个提供商，{providers.filter(p => p.enabled).length} 个已启用，{Object.keys(selectedModels).length} 个已选择模型
          </p>
        </div>
        <div className="flex space-x-2">
          <Button 
            variant="outline" 
            onClick={handleTestAllProviders}
            disabled={testingAll || providers.length === 0}
          >
            {testingAll ? (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <Zap className="w-4 h-4 mr-2" />
            )}
            测试所有连接
          </Button>
          <Button onClick={() => setShowAddDialog(true)}>
            <Plus className="w-4 h-4 mr-2" />
            添加提供商
          </Button>
        </div>
      </div>

      {/* 提供商列表 */}
      <div className="grid gap-4">
        {providers.map((provider) => {
          const providerInfo = getProviderInfo(provider.type)
          const connectionStatus = getConnectionStatus(provider.id)
          const connectionResult = connectionResults[provider.id]
          
          return (
            <Card key={provider.id} className={`transition-all ${provider.enabled ? '' : 'opacity-60'}`}>
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-4 flex-1">
                    {/* 提供商图标 */}
                    <div className="text-2xl">{providerInfo?.icon || '🤖'}</div>
                    
                    {/* 提供商信息 */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="font-semibold text-lg">{provider.name}</h3>
                        <Badge variant="outline">{providerInfo?.name || provider.type}</Badge>
                        
                        {/* 状态指示器 */}
                        <div className="flex items-center space-x-2">
                          <div className={`w-2 h-2 rounded-full ${getStatusColor(connectionStatus)}`} />
                          <span className="text-sm text-muted-foreground">
                            {getStatusText(connectionStatus)}
                          </span>
                          {connectionResult?.responseTime && (
                            <span className="text-xs text-muted-foreground">
                              ({connectionResult.responseTime}ms)
                            </span>
                          )}
                        </div>
                      </div>
                      
                      {providerInfo?.description && (
                        <p className="text-sm text-muted-foreground mb-3">{providerInfo.description}</p>
                      )}
                      
                      {/* 连接错误信息 */}
                      {connectionResult?.error && (
                        <Alert variant="destructive" className="mb-3">
                          <AlertCircle className="h-4 w-4" />
                          <AlertDescription className="text-xs">
                            {connectionResult.error}
                          </AlertDescription>
                        </Alert>
                      )}
                      
                      {/* API配置信息 */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div>
                          <Label className="text-xs text-muted-foreground">API地址</Label>
                          <p className="font-mono text-xs bg-muted px-2 py-1 rounded truncate">
                            {provider.baseUrl}
                          </p>
                        </div>
                        
                        {provider.apiKey && (
                          <div>
                            <Label className="text-xs text-muted-foreground">API密钥</Label>
                            <div className="flex items-center space-x-2">
                              <p className="font-mono text-xs bg-muted px-2 py-1 rounded flex-1">
                                {showApiKey[provider.id] 
                                  ? provider.apiKey 
                                  : '•'.repeat(Math.min(provider.apiKey.length, 20))
                                }
                              </p>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => toggleApiKeyVisibility(provider.id)}
                              >
                                {showApiKey[provider.id] ? (
                                  <EyeOff className="w-3 h-3" />
                                ) : (
                                  <Eye className="w-3 h-3" />
                                )}
                              </Button>
                            </div>
                          </div>
                        )}
                        
                        {provider.timeout && (
                          <div>
                            <Label className="text-xs text-muted-foreground">超时时间</Label>
                            <p className="text-xs">{provider.timeout}ms</p>
                          </div>
                        )}
                        
                        {connectionResult?.modelCount && (
                          <div>
                            <Label className="text-xs text-muted-foreground">可用模型</Label>
                            <p className="text-xs">{connectionResult.modelCount} 个</p>
                          </div>
                        )}
                        
                        {selectedModels[provider.id] && (
                          <div>
                            <Label className="text-xs text-muted-foreground">已选模型</Label>
                            <p className="text-xs font-medium text-primary">{selectedModels[provider.id]}</p>
                          </div>
                        )}

                        {modelTestResults[provider.id] && (
                          <div>
                            <Label className="text-xs text-muted-foreground">模型测试</Label>
                            <div className={`text-xs p-2 rounded border ${
                              modelTestResults[provider.id].success
                                ? 'bg-green-50 border-green-200 text-green-800'
                                : 'bg-red-50 border-red-200 text-red-800'
                            }`}>
                              {modelTestResults[provider.id].success ? (
                                <div>
                                  <div className="font-medium mb-1">✅ 测试成功</div>
                                  {modelTestResults[provider.id].modelName && (
                                    <div className="text-xs opacity-70 mb-1">
                                      模型: {modelTestResults[provider.id].modelName}
                                    </div>
                                  )}
                                  <div className="text-xs opacity-80">
                                    {modelTestResults[provider.id].response}
                                  </div>
                                </div>
                              ) : (
                                <div>
                                  <div className="font-medium mb-1">❌ 测试失败</div>
                                  <div className="text-xs opacity-80">
                                    {modelTestResults[provider.id].error}
                                  </div>
                                </div>
                              )}
                              <div className="text-xs opacity-60 mt-1">
                                {modelTestResults[provider.id].timestamp.toLocaleTimeString()}
                              </div>
                            </div>
                          </div>
                        )}
                        
                        <div>
                          <Label className="text-xs text-muted-foreground">创建时间</Label>
                          <p className="text-xs">{provider.createdAt.toLocaleString()}</p>
                        </div>
                        
                        <div>
                          <Label className="text-xs text-muted-foreground">更新时间</Label>
                          <p className="text-xs">{provider.updatedAt.toLocaleString()}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {/* 操作按钮 */}
                  <div className="flex items-center space-x-2 ml-4">
                    <Switch
                      checked={provider.enabled}
                      onCheckedChange={() => handleToggleProvider(provider.id)}
                    />
                    
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleTestProvider(provider.id)}
                      disabled={testingProvider === provider.id}
                      title="测试连接"
                    >
                      {testingProvider === provider.id ? (
                        <Loader2 className="w-4 h-4 animate-spin" />
                      ) : (
                        <Zap className="w-4 h-4" />
                      )}
                    </Button>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleTestModel(provider.id)}
                      disabled={testingModel === provider.id || !provider.enabled || (providerModels[provider.id] || []).length === 0}
                      title="测试模型对话"
                    >
                      {testingModel === provider.id ? (
                        <Loader2 className="w-4 h-4 animate-spin" />
                      ) : (
                        <MessageCircle className="w-4 h-4" />
                      )}
                    </Button>
                    
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setEditingProvider(provider)}
                    >
                      <Edit className="w-4 h-4" />
                    </Button>
                    
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDeleteProvider(provider.id)}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
                
                {/* 模型选择区域 - 连接成功后显示 */}
                {connectionResult?.success && (
                  <div className="mt-4 pt-4 border-t">
                    <ModelSelector
                      providerId={provider.id}
                      providerName={provider.name}
                      models={providerModels[provider.id] || []}
                      selectedModelId={selectedModels[provider.id]}
                      isLoading={loadingModels[provider.id]}
                      onModelSelect={(modelId) => handleSelectModel(provider.id, modelId)}
                      onRefreshModels={() => loadProviderModels(provider.id)}
                    />
                  </div>
                )}
              </CardContent>
            </Card>
          )
        })}
        
        {providers.length === 0 && (
          <Card>
            <CardContent className="p-8 text-center">
              <Bot className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
              <h3 className="text-lg font-semibold mb-2">暂无AI提供商</h3>
              <p className="text-muted-foreground mb-4">
                点击"添加提供商"按钮开始配置您的第一个AI提供商
              </p>
              <Button onClick={() => setShowAddDialog(true)}>
                <Plus className="w-4 h-4 mr-2" />
                添加提供商
              </Button>
            </CardContent>
          </Card>
        )}
      </div>

      {/* 使用说明 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <AlertCircle className="w-4 h-4 mr-2" />
            使用说明
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-sm space-y-2">
            <p><strong>支持的AI提供商：</strong></p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2 ml-4">
              {supportedProviders.map(provider => (
                <div key={provider.id} className="flex items-center space-x-2">
                  <span>{provider.icon}</span>
                  <span>{provider.name}：{provider.description}</span>
                </div>
              ))}
            </div>
            
            <p className="mt-3"><strong>配置说明：</strong></p>
            <ul className="list-disc list-inside ml-4 space-y-1">
              <li>API密钥：从对应提供商获取的访问密钥（本地服务可选）</li>
              <li>API地址：模型服务的接口地址</li>
              <li>连接测试：验证配置是否正确并获取可用模型数量</li>
              <li>启用状态：控制提供商是否在系统中可用</li>
            </ul>
            
            <p className="mt-3"><strong>操作提示：</strong></p>
            <ul className="list-disc list-inside ml-4 space-y-1">
              <li>点击"测试连接"验证单个提供商配置</li>
              <li>点击"测试所有连接"批量验证所有提供商</li>
              <li>使用开关控制提供商的启用状态</li>
              <li>点击眼睛图标查看/隐藏API密钥</li>
            </ul>
          </div>
        </CardContent>
      </Card>

      {/* 添加提供商对话框 */}
      <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>添加AI提供商</DialogTitle>
            <DialogDescription>
              配置新的AI提供商以集成到您的工作流程中
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            {/* 基本信息 */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="provider-name">提供商名称</Label>
                <Input
                  id="provider-name"
                  value={newProvider.name}
                  onChange={(e) => setNewProvider(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="例如：我的OpenAI"
                />
              </div>
              
              <div>
                <Label htmlFor="provider-type">提供商类型</Label>
                <Select
                  value={newProvider.type}
                  onValueChange={handleProviderTypeChange}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {supportedProviders.map(provider => (
                      <SelectItem key={provider.id} value={provider.type}>
                        <div className="flex items-center space-x-2">
                          <span>{provider.icon}</span>
                          <span>{provider.name}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            {/* API配置 */}
            <div>
              <Label htmlFor="base-url">API地址</Label>
              <Input
                id="base-url"
                value={newProvider.baseUrl}
                onChange={(e) => setNewProvider(prev => ({ ...prev, baseUrl: e.target.value }))}
                placeholder="https://api.openai.com/v1"
              />
            </div>
            
            <div>
              <Label htmlFor="api-key">
                API密钥
                {supportedProviders.find(p => p.type === newProvider.type)?.requiresApiKey === false && (
                  <span className="text-xs text-muted-foreground ml-2">（可选）</span>
                )}
              </Label>
              <Input
                id="api-key"
                type="password"
                value={newProvider.apiKey}
                onChange={(e) => setNewProvider(prev => ({ ...prev, apiKey: e.target.value }))}
                placeholder={
                  supportedProviders.find(p => p.type === newProvider.type)?.requiresApiKey === false
                    ? "本地服务通常不需要API密钥"
                    : "输入您的API密钥"
                }
              />
            </div>
            
            {/* 高级配置 */}
            <div>
              <Label htmlFor="timeout">超时时间（毫秒）</Label>
              <Input
                id="timeout"
                type="number"
                value={newProvider.timeout || ''}
                onChange={(e) => setNewProvider(prev => ({ 
                  ...prev, 
                  timeout: e.target.value ? parseInt(e.target.value) : undefined 
                }))}
                placeholder="60000"
              />
            </div>
            
            {/* 提供商信息显示 */}
            {supportedProviders.find(p => p.type === newProvider.type) && (
              <div className="p-4 bg-muted rounded-lg">
                <h4 className="font-medium mb-2">提供商信息</h4>
                <p className="text-sm text-muted-foreground">
                  {supportedProviders.find(p => p.type === newProvider.type)?.description}
                </p>
                {supportedProviders.find(p => p.type === newProvider.type)?.documentationUrl && (
                  <a 
                    href={supportedProviders.find(p => p.type === newProvider.type)?.documentationUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-xs text-primary hover:underline mt-1 inline-block"
                  >
                    查看官方文档 →
                  </a>
                )}
              </div>
            )}
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAddDialog(false)}>
              取消
            </Button>
            <Button 
              onClick={handleAddProvider} 
              disabled={!newProvider.name.trim() || !newProvider.baseUrl.trim()}
            >
              添加提供商
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default AIIntegrationTab